# AI监工2.0系统流程图

## 系统概述
基于现有监工链路优化，新增多维度异常监控能力，实现预提醒监工+催收多维监工双重监控机制。

## 监工2.0流程图

### 预提醒监工（准实时链路）

```mermaid
flowchart TD
    A1[数据入口] --> B1[意图结果处理]
    B1 --> C1[LLM: 意图初检]
    B1 --> C2[RAG: 意图语料库召回]

    C1 --> D1[检查结果处理]
    C2 --> D1

    D1 --> E1[LLM: 复检+错误原因分析+优化意图建议]

    E1 --> F1{检测到异常?}
    F1 -->|是| G1[企微触达]
    F1 -->|否| H1[无异常结束]

    G1 --> I1[触达系统打标]
    I1 --> J1[意图标注页面跟踪]
    J1 --> K1[标注跟进状态<br/>形成闭环]

    %% 样式定义
    style A1 fill:#ff9999,stroke:#333,stroke-width:3px
    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style C1 fill:#fbb,stroke:#333,stroke-width:2px
    style C2 fill:#fbb,stroke:#333,stroke-width:2px
    style D1 fill:#bfb,stroke:#333,stroke-width:2px
    style E1 fill:#fbf,stroke:#333,stroke-width:2px
    style F1 fill:#fff3cd,stroke:#ffc107,stroke-width:2px
    style G1 fill:#bff,stroke:#333,stroke-width:2px
    style H1 fill:#e8f5e8,stroke:#333,stroke-width:2px
    style I1 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
    style J1 fill:#f0f8ff,stroke:#333,stroke-width:2px
    style K1 fill:#ffebee,stroke:#333,stroke-width:2px
```

### 催收多维监工（准实时链路）

```mermaid
flowchart TD
    A2[会话数据入口] --> B2[会话数据完整性检查]
    B2 --> C3[多维度异常分析引擎]

    C3 --> D2[系统异常检测模块]
    C3 --> D3[业务异常检测模块]
    C3 --> D4[话术质量检测模块]

    %% 系统异常检测详细流程
    D2 --> D2a[响应耗时检测<br/>阈值: ≥3秒]
    D2 --> D2b[系统静默检测<br/>阈值: ≥5秒无响应]
    D2 --> D2c[系统报错检测<br/>异常日志分析]

    %% 业务异常检测详细流程
    D3 --> D3a[轮次异常检测<br/>系统挂断且≤4轮]
    D3 --> D3b[异常挂断检测<br/>未命中结束语判断]
    D3 --> D3c[通时异常检测<br/>单次通话≥5分钟]

    %% 话术质量检测详细流程
    D4 --> D4a[话术匹配检测<br/>LLM评估匹配度]
    D4 --> D4b[意图分类检测<br/>一级意图准确性]
    D4 --> D4c[话术优化检测<br/>更优模型重评估]
    D4 --> D4d[话术评分检测<br/>质量阈值判断]

    %% 异常汇总与智能分析
    D2a --> E2[异常结果汇总中心]
    D2b --> E2
    D2c --> E2
    D3a --> E2
    D3b --> E2
    D3c --> E2
    D4a --> E2
    D4b --> E2
    D4c --> E2
    D4d --> E2

    E2 --> F2[LLM: 综合分析+优化建议生成]

    F2 --> G2{检测到异常?}
    G2 -->|是| H2[企微触达]
    G2 -->|否| I2[无异常结束]

    H2 --> J2[触达系统打标]
    J2 --> K2[AI标注页面跟踪]
    K2 --> L2[标注跟进状态<br/>形成闭环]

    %% 样式定义
    style A2 fill:#ff9999,stroke:#333,stroke-width:3px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style C3 fill:#f3e5f5,stroke:#333,stroke-width:2px
    style D2 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
    style D3 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
    style D4 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
    style E2 fill:#e8f5e8,stroke:#333,stroke-width:2px
    style F2 fill:#fff9c4,stroke:#333,stroke-width:2px
    style G2 fill:#fff3cd,stroke:#ffc107,stroke-width:2px
    style H2 fill:#bff,stroke:#333,stroke-width:2px
    style I2 fill:#e8f5e8,stroke:#333,stroke-width:2px
    style J2 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
    style K2 fill:#f0f8ff,stroke:#333,stroke-width:2px
    style L2 fill:#ffebee,stroke:#333,stroke-width:2px
```

## 异常类型定义

### 1. 系统异常类型
- **响应超时**: 灵犀响应时间≥3秒
- **系统静默**: 系统无响应时间≥5秒
- **系统报错**: 捕获到系统异常日志

### 2. 业务异常类型
- **轮次过少**: 系统侧挂断但轮次≤4轮
- **异常挂断**: 系统侧挂断但未命中结束语
- **通时过长**: 单次通话时长≥5分钟

### 3. 话术质量异常
- **话术不匹配**: 回复内容与用户意图不符
- **意图分类错误**: 一级意图大类识别错误
- **话术非最优**: 存在更优话术选择
- **话术评分过低**: 话术质量评分低于阈值

## 技术实现要点

### 1. 实时监控链路优化
- 保持现有监控逻辑不变
- 增强异常检测算法精度
- 优化LLM调用效率

### 2. 事后监控链路设计
- 基于会话结束事件触发
- 多维度并行异常检测
- 智能异常聚合与分析

### 3. 数据流转机制
- 统一数据接口标准
- 异步处理提升性能
- 容错机制保障稳定性

### 4. 展示与触达
- 新增异常类型Tab页
- 支持实时和批量触达
- 提供详细异常描述和建议

## 部署建议

### 阶段一：基础能力建设（1-2周）
- 完善现有实时监控链路
- 开发事后监控核心模块
- 建立异常数据模型

### 阶段二：功能集成测试（2-3周）
- 集成多维度异常检测
- 完善触达机制
- 系统联调测试

### 阶段三：上线与优化（1-2周）
- 灰度发布验证
- 性能调优
- 用户反馈收集与优化
