<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI监工2.0系统流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        .description {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .mermaid {
            text-align: center;
            margin: 30px 0;
        }

        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .legend-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
        }
        .legend-item h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .legend-item ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .legend-item li {
            margin: 5px 0;
            color: #34495e;
        }
        .highlight {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .new-feature {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>AI监工2.0系统流程图</h1>

        <div class="description">
            <h3>系统概述</h3>
            <p>AI监工2.0在现有监工链路基础上，新增多维度异常监控能力，实现<strong>预提醒监工</strong>与<strong>催收多维监工</strong>双重保障机制。系统能够检测9大类异常情况，提供智能化的异常分析和优化建议。</p>
        </div>

        <!-- 预提醒监工 -->
        <div style="margin: 30px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">预提醒监工（准实时链路）</h2>
            <div class="mermaid">
                    flowchart TD
                        A1[数据入口] --> B1[意图结果处理]
                        B1 --> C1[LLM: 意图初检]
                        B1 --> C2[RAG: 意图语料库召回]

                        C1 --> D1[检查结果处理]
                        C2 --> D1

                        D1 --> E1[LLM: 复检+错误原因分析+优化意图建议]

                        E1 --> F1{检测到异常?}
                        F1 -->|是| G1[企微触达]
                        F1 -->|否| H1[无异常结束]

                        G1 --> I1[触达系统打标]
                        I1 --> J1[意图标注页面展示]
                        J1 -->|跟踪修复进度| J1

                        %% 样式定义
                        style A1 fill:#ff9999,stroke:#333,stroke-width:3px
                        style B1 fill:#bbf,stroke:#333,stroke-width:2px
                        style C1 fill:#fbb,stroke:#333,stroke-width:2px
                        style C2 fill:#fbb,stroke:#333,stroke-width:2px
                        style D1 fill:#bfb,stroke:#333,stroke-width:2px
                        style E1 fill:#fbf,stroke:#333,stroke-width:2px
                        style F1 fill:#fff3cd,stroke:#ffc107,stroke-width:2px
                        style G1 fill:#bff,stroke:#333,stroke-width:2px
                        style H1 fill:#e8f5e8,stroke:#333,stroke-width:2px
                        style I1 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style J1 fill:#f0f8ff,stroke:#333,stroke-width:2px
                </div>
            </div>

        <!-- 催收多维监工 -->
        <div style="margin: 30px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">催收多维监工（准实时链路）</h2>
            <div class="mermaid">
                    flowchart TD
                        A2[会话数据入口] --> B2[会话数据完整性检查]
                        B2 --> C3[多维度异常分析引擎]

                        C3 --> D2[系统异常检测模块]
                        C3 --> D3[业务异常检测模块]
                        C3 --> D4[话术质量检测模块]

                        %% 系统异常检测详细流程
                        D2 --> D2a[响应耗时<br/>≥3秒]
                        D2 --> D2b[系统静默<br/>≥5秒]
                        D2 --> D2c[系统报错<br/>异常日志]

                        %% 业务异常检测详细流程
                        D3 --> D3a[轮次过少<br/>≤4轮]
                        D3 --> D3b[异常挂断<br/>未命中结束语]
                        D3 --> D3c[通时过长<br/>≥5分钟]

                        %% 话术质量检测详细流程
                        D4 --> D4a[话术匹配<br/>LLM评估]
                        D4 --> D4b[意图分类<br/>准确性检测]
                        D4 --> D4c[话术优化<br/>更优选择]
                        D4 --> D4d[话术评分<br/>质量阈值]

                        %% 异常汇总与智能分析
                        D2a --> E2[异常结果汇总中心]
                        D2b --> E2
                        D2c --> E2
                        D3a --> E2
                        D3b --> E2
                        D3c --> E2
                        D4a --> E2
                        D4b --> E2
                        D4c --> E2
                        D4d --> E2

                        E2 --> F2[LLM: 综合分析+优化建议生成]

                        F2 --> G2{检测到异常?}
                        G2 -->|是| H2[企微触达]
                        G2 -->|否| I2[无异常结束]

                        H2 --> J2[触达系统打标]
                        J2 --> K2[AI标注页面展示]
                        K2 -->|跟踪修复进度| K2

                        %% 样式定义
                        style A2 fill:#ff9999,stroke:#333,stroke-width:3px
                        style B2 fill:#bbf,stroke:#333,stroke-width:2px
                        style C3 fill:#f3e5f5,stroke:#333,stroke-width:2px
                        style D2 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style D3 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style D4 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style E2 fill:#e8f5e8,stroke:#333,stroke-width:2px
                        style F2 fill:#fff9c4,stroke:#333,stroke-width:2px
                        style G2 fill:#fff3cd,stroke:#ffc107,stroke-width:2px
                        style H2 fill:#bff,stroke:#333,stroke-width:2px
                        style I2 fill:#e8f5e8,stroke:#333,stroke-width:2px
                        style J2 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style K2 fill:#f0f8ff,stroke:#333,stroke-width:2px
                </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <h3>🔍 系统异常监控</h3>
                <ul>
                    <li><strong>响应超时</strong>: 灵犀响应时间≥3秒</li>
                    <li><strong>系统静默</strong>: 系统无响应≥5秒</li>
                    <li><strong>系统报错</strong>: 捕获异常日志并分析</li>
                </ul>
            </div>

            <div class="legend-item">
                <h3>📊 业务异常监控</h3>
                <ul>
                    <li><strong>轮次过少</strong>: 系统挂断但对话≤4轮</li>
                    <li><strong>异常挂断</strong>: 未命中结束语的系统挂断</li>
                    <li><strong>通时过长</strong>: 单次通话时长≥5分钟</li>
                </ul>
            </div>

            <div class="legend-item">
                <h3>💬 话术质量监控</h3>
                <ul>
                    <li><strong>话术不匹配</strong>: 回复与用户意图不符</li>
                    <li><strong>意图分类错误</strong>: 一级意图识别错误</li>
                    <li><strong>话术非最优</strong>: 存在更优话术选择</li>
                    <li><strong>话术评分过低</strong>: 质量评分低于阈值</li>
                </ul>
            </div>

            <div class="legend-item highlight">
                <h3>⚡ 预提醒监工链路</h3>
                <ul>
                    <li>沿用现有监工架构</li>
                    <li>优化LLM调用效率</li>
                    <li>增强异常检测精度</li>
                    <li>意图标注页面跟踪闭环</li>
                </ul>
            </div>

            <div class="legend-item new-feature">
                <h3>🆕 催收多维监工链路</h3>
                <ul>
                    <li>基于会话数据准实时触发</li>
                    <li>多维度并行异常检测</li>
                    <li>智能异常聚合分析</li>
                    <li>AI标注页面跟踪闭环</li>
                </ul>
            </div>

            <div class="legend-item new-feature">
                <h3>🎯 闭环跟踪机制</h3>
                <ul>
                    <li><strong>预提醒监工</strong>: 意图标注页面跟踪</li>
                    <li><strong>催收多维监工</strong>: AI标注页面跟踪</li>
                    <li><strong>状态标注</strong>: 可标注跟进状态</li>
                    <li><strong>形成闭环</strong>: 完整的监控-触达-跟踪流程</li>
                </ul>
            </div>
        </div>


    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
